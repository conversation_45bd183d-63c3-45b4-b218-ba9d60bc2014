# Chart Grouping Feature

## Overview
The chart grouping feature allows charts to be organized into logical groups based on the `chart_group` field in the API response. This provides a better user experience by:

1. Displaying only the first chart from each group on the main dashboard
2. Showing a "More Charts" button when a group contains multiple charts
3. Opening a dedicated page to view all charts from a selected group

## API Response Format
The API response now includes a `chart_group` field for each chart:

```json
{
  "charts": [
    {
      "chart_type": "pie",
      "chart_group": "basic",
      "library": "plotly",
      "data": { ... },
      "layout": { ... }
    },
    {
      "chart_type": "bar", 
      "chart_group": "basic",
      "library": "plotly",
      "data": { ... },
      "layout": { ... }
    },
    {
      "chart_type": "histogram",
      "chart_group": "numerical",
      "library": "plotly", 
      "data": { ... },
      "layout": { ... }
    }
  ]
}
```

## Implementation Details

### Components Added/Modified

1. **ChartGroupPage.tsx** (New)
   - Displays all charts from a selected group
   - Provides navigation back to the main dashboard
   - Maintains the same 3-column grid layout

2. **ChartGrid.tsx** (Modified)
   - Groups charts by `chart_group` field
   - Displays only the first chart from each group
   - Shows "More Charts" button with count for groups with multiple charts
   - Added `onGroupClick` callback prop

3. **Dashboard.tsx** (Modified)
   - Added state management for current view and selected group
   - Added navigation handlers for group page
   - Conditionally renders either dashboard or group page

4. **Chart.tsx** (Modified)
   - Updated ChartData interface to include optional `chart_group` field

### User Experience

#### Main Dashboard
- Shows one representative chart per group
- Groups with multiple charts display a blue "More Charts" button in the top-right corner
- Button shows "+N" where N is the number of additional charts in the group
- Clicking the button navigates to the group page

#### Group Page
- Shows all charts from the selected group in a 3-column grid
- Header displays the group name and total chart count
- Back button returns to the main dashboard
- Maintains all chart functionality (insights, export, etc.)

### Chart Grouping Logic
- Charts are grouped by the `chart_group` field value
- Charts without a `chart_group` field are placed in an "ungrouped" category
- Groups are displayed in the order they appear in the API response
- Within each group, the first chart is used as the representative

### Styling
- "More Charts" button uses Material-UI styling with blue background
- Group page header uses the same gradient styling as the main dashboard
- Maintains consistent spacing and layout across all views

## Testing
A sample test file is provided at `src/test-data/sample-grouped-charts.json` with example grouped chart data.

A test component `TestChartGrouping.tsx` is also available to verify the grouping functionality works correctly.

## Bug Fix Applied
**Issue**: Only one chart was being displayed on the dashboard instead of one chart per group.

**Root Cause**: The `FileUpload.tsx` component was not including the `chart_group` field when processing the API response, causing all charts to be grouped under "ungrouped".

**Fix**: Updated the `FileUpload.tsx` component to include the `chart_group` field in the processed chart data:

```typescript
const processedCharts = data.charts.map((chart: any) => {
  return {
    chart_type: chart.chart_type,
    chart_group: chart.chart_group, // Added this line
    library: chart.library,
    data: chart.data,
    layout: chart.layout
  };
});
```

Now the dashboard correctly displays one representative chart from each group with appropriate "More Charts" buttons.

## Future Enhancements
- Add group filtering/search functionality
- Allow users to customize which chart represents each group
- Add group-level analytics and insights
- Support for nested/hierarchical grouping
