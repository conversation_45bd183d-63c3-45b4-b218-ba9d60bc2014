import { createTheme } from '@mui/material';

export const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#2196f3', // keeping vibrant blue for actions
      light: '#4dabf5',
      dark: '#1769aa',
    },
    secondary: {
      main: '#757575', // professional gray
      light: '#9e9e9e',
      dark: '#616161',
    },
    background: {
      default: '#f5f5f5', // light gray background
      paper: '#ffffff',    // white paper
    },
    text: {
      primary: '#212121',
      secondary: 'rgba(0, 0, 0, 0.7)',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
      marginBottom: '1rem',
      color: '#212121',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 500,
      color: '#212121',
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 500,
      color: '#212121',
    },
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        },
      },
    },
  },
});
