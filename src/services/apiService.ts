// src/services/apiService.ts

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// UUID generator
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

// Device ID fetcher
export function getDeviceId(): string {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = generateUUID();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
}

// Session ID fetcher (unique per login session)
export function getSessionId(): string {
  let sessionId = sessionStorage.getItem('session_id');
  if (!sessionId) {
    sessionId = generateUUID();
    sessionStorage.setItem('session_id', sessionId);
  }
  return sessionId;
}

// Call this on successful login to generate new session id
export function initializeSession() {
  const newSessionId = generateUUID();
  sessionStorage.setItem('session_id', newSessionId);
  return newSessionId;
}

// Always get access_token from localStorage for API calls
export function getAuthToken(): string | null {
  return localStorage.getItem('access_token');
}

export function getRefreshToken(): string | null {
  return localStorage.getItem('refresh_token');
}

export function setAuthToken(token: string) {
  localStorage.setItem('access_token', token);
}

export function setRefreshToken(token: string) {
  localStorage.setItem('refresh_token', token);
}

export function clearAuthToken() {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
}

// Centralized header builder for JSON APIs
export function getHeaders(
  contentType: string | null = 'application/json'
): Headers {
  const headers = new Headers();
  headers.append('x_interaction_id', generateUUID());
  headers.append('x_session_id', getSessionId());
  headers.append('x_device_id', getDeviceId());
  if (contentType) headers.append('Content-Type', contentType);

  const token = getAuthToken();
  if (token) headers.append('Authorization', `Bearer ${token}`);

  return headers;
}

// Export tryRefreshToken for use in App.tsx
export async function tryRefreshToken() {
  const refreshToken = getRefreshToken();
  if (!refreshToken) return false;
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
      method: 'POST',
      headers: new Headers({ 'Content-Type': 'application/json' }),
      body: JSON.stringify({ refresh_token: refreshToken }),
    });
    if (!response.ok) return false;
    const data = await response.json();
    if (data.access_token) {
      setAuthToken(data.access_token);
      return true;
    }
    return false;
  } catch {
    return false;
  }
}

// Wrapper for API calls with token refresh logic
async function fetchWithAuthRetry(
  url: string,
  options: RequestInit,
  retry = true
): Promise<Response> {
  let token = getAuthToken();
  if (token && options.headers instanceof Headers) {
    options.headers.set('Authorization', `Bearer ${token}`);
  }
  let response = await fetch(url, options);
  if (response.status === 401 && retry) {
    const refreshed = await tryRefreshToken();
    if (refreshed) {
      token = getAuthToken();
      if (token && options.headers instanceof Headers) {
        options.headers.set('Authorization', `Bearer ${token}`);
      }
      response = await fetch(url, options);
    }
  }
  return response;
}

/**
 * Get insights for a chart
 */
export const getChartInsights = async (
  chartId: string | number,
  chartData: any
): Promise<any> => {
 
  try {
    const headers = getHeaders();
    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/chart/executive-summary`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify({
          chart_id: chartId,
          chart_data: chartData,
        }),
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to get insights: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error getting chart insights:', error);
    throw error;
  }
};

/**
 * Update chart data
 */
export const updateChart = async (
  chartId: string | number,
  updatedData: any
): Promise<any> => {
 
  try {
    const headers = getHeaders();
    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/chart/data`,
      {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          chart_id: chartId,
          updated_data: updatedData,
        }),
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to update chart: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error updating chart:', error);
    throw error;
  }
};

/**
 * Upload and process CSV data
 */
export const uploadCSV = async (
  formData: FormData
): Promise<any> => {
  const deviceId = getDeviceId();
  try {
    const headers = new Headers();
    headers.append('X-Interaction-Id', generateUUID());
    headers.append('X-Session-Id', getSessionId());
    headers.append('X-Device-Id', deviceId);
    // Do not set Content-Type for FormData
    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/data/file-upload`,
      {
        method: 'POST',
        headers,
        body: formData,
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to upload CSV: ${JSON.stringify(errorData)}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error uploading CSV:', error);
    throw error;
  }
};

/**
 * Google OAuth Login
 */
export const googleLogin = async (googleToken: string): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/google`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ token: googleToken }),
    });

    if (!response.ok) {
      throw new Error(`Google login failed: ${response.status}`);
    }
    const data = await response.json();
    // Save token after successful login
    if (data.access_token) {
      setAuthToken(data.access_token);
      initializeSession();
    }

    return data;
  } catch (error) {
    console.error('Error during Google login:', error);
    throw error;
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (refreshToken: string): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error refreshing token:', error);
    throw error;
  }
};
