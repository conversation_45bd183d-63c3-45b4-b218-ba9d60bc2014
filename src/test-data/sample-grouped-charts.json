{"charts": [{"chart_type": "pie", "chart_group": "basic", "library": "plotly", "data": {"values": [30, 25, 20, 15, 10], "labels": ["Category A", "Category B", "Category C", "Category D", "Category E"], "type": "pie"}, "layout": {"title": "Distribution of Categories", "showlegend": true}}, {"chart_type": "bar", "chart_group": "basic", "library": "plotly", "data": {"x": ["A", "B", "C", "D"], "y": [20, 14, 23, 25], "type": "bar"}, "layout": {"title": "Bar Chart Example", "xaxis": {"title": "Categories"}, "yaxis": {"title": "Values"}}}, {"chart_type": "histogram", "chart_group": "numerical", "library": "plotly", "data": {"x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "type": "histogram"}, "layout": {"title": "Distribution of Values", "xaxis": {"title": "Value"}, "yaxis": {"title": "Frequency"}}}, {"chart_type": "scatter", "chart_group": "numerical", "library": "plotly", "data": {"x": [1, 2, 3, 4, 5], "y": [2, 4, 3, 5, 6], "type": "scatter", "mode": "markers"}, "layout": {"title": "Scatter Plot Example", "xaxis": {"title": "X Values"}, "yaxis": {"title": "Y Values"}}}, {"chart_type": "line", "chart_group": "time_series", "library": "plotly", "data": {"x": ["Jan 2024", "Feb 2024", "Mar 2024"], "y": [100, 120, 110], "type": "scatter", "mode": "lines"}, "layout": {"title": "Trend Over Time", "xaxis": {"title": "Date"}, "yaxis": {"title": "Value"}}}, {"chart_type": "doughnut", "chart_group": "basic", "library": "plotly", "data": {"values": [237, 246, 259, 258], "labels": ["East", "North", "South", "West"], "type": "pie", "hole": 0.4}, "layout": {"title": "Doughnut Chart: Distribution of sales_region", "showlegend": true}}], "data_quality": {}, "analytics": {}}