import React from "react";
import {
  Box,
  But<PERSON>,
  Container,
  Typography,
  useTheme,
  Paper,
} from "@mui/material";

// Import your screenshots
import chart1 from "../assets/chart1.png";
import chart2 from "../assets/chart2.png";
import chart3 from "../assets/chart3.png";
import chart4 from "../assets/chart4.png";

const Home: React.FC<{ onStart: () => void }> = ({ onStart }) => {
  const theme = useTheme();

  return (
    <Box sx={{ minHeight: "100vh", bgcolor: "#FFFFFF", display: "flex", flexDirection: "column" }}>

      {/* Hero Section */}
      <Box
        component="header"
        sx={{
          flexGrow: 0,
          py: { xs: 6, md: 8 },
          textAlign: "center",
          bgcolor: "#FFFFFF",
        }}
      >
        <Container maxWidth="md">
          <Typography
            variant="h2"
            component="h1"
            sx={{
              fontSize: { xs: "2.5rem", md: "3.5rem" },
              fontWeight: "bold",
              mb: 2,
              lineHeight: 1.2,
            }}
          >
            Your Data, Beautifully Charted
          </Typography>
          <Typography
            variant="h5"
            color="text.secondary"
            sx={{ mb: 3, fontSize: { xs: "0.9rem", md: "1.25rem" } }}
          >
            Start Telling Better Data Stories Today
          </Typography>

        

          {/* Image Showcase */}
          <Box
            sx={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "center",
              alignItems: "center",
              gap: 3,
              mt: 5,
              mb: 5,
              perspective: "1000px",
            }}
          >
            {[chart1, chart2, chart3, chart4].map((img, index) => (
              <Box
                key={index}
                component="img"
                src={img}
                alt={`chart ${index + 1}`}
                sx={{
                  width: { xs: "130px", sm: "160px", md: "180px" },
                  borderRadius: 2,
                  boxShadow: "0 12px 24px rgba(0,0,0,0.2)",
                  transform: [
                    "rotate(-10deg) translateZ(10px)",
                    "rotate(8deg) translateZ(10px)",
                    "rotate(-6deg) translateZ(10px)",
                    "rotate(12deg) translateZ(10px)"
                  ][index],
                  transition: "transform 0.4s ease, box-shadow 0.4s ease",
                  "&:hover": {
                    transform: "scale(1.08) rotate(0deg)",
                    boxShadow: "0 20px 40px rgba(0,0,0,0.3)",
                    zIndex: 5,
                  },
                }}
              />
            ))}
          </Box>

          <Button
            variant="contained"
            color="secondary"
            size="large"
            onClick={onStart}
            sx={{
              textTransform: "none",
              px: 5,
              py: 1.5,
              fontWeight: "bold",
              boxShadow: "none",
              "&:hover": {
                boxShadow: theme.shadows[4],
              },
            }}
          >
            Get Started Free
          </Button>

          {/* Disclaimer message */}
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mt: 3, fontSize: { xs: "0.95rem", md: "1.05rem" }, fontStyle: "italic" }}
          >
            Your Data is not stored anywhere, not used for any training, only for preparing chart.
          </Typography>
        </Container>
      </Box>

      {/* Cards Section */}
      <Box component="section" sx={{ flexGrow: 0, py: { xs: 3, md: 5 }, bgcolor: "#FFFFFF" }}>
        <Container maxWidth="lg">
          <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: 2 }}>
            
            <Box sx={{ flex: '1 1 300px', minWidth: 250, maxWidth: 400, mb: 2 }}>
              <Paper elevation={3} sx={{ p: 2.5, borderRadius: 3, bgcolor: "#FFF3E0" }}>
                <Typography variant="h5" sx={{ fontWeight: "bold", mb: 1.5, textAlign: "center", fontSize: "1.09rem" }}>
                  Get Started in 3 Easy Steps
                </Typography>
                <Box sx={{ display: "flex", flexDirection: "column", gap: 0.75 }}>
                  <Typography sx={{ fontSize: "0.9rem" }}>1️⃣ Upload your CSV</Typography>
                  <Typography sx={{ fontSize: "0.9rem" }}>2️⃣ Instant insights & visuals</Typography>
                  <Typography sx={{ fontSize: "0.9rem" }}>3️⃣ Copy charts into presentations</Typography>
                </Box>
              </Paper>
            </Box>
            <Box sx={{ flex: '1 1 300px', minWidth: 250, maxWidth: 400, mb: 2 }}>
              <Paper elevation={3} sx={{ p: 2.5, borderRadius: 3, bgcolor: "#E8F5E9" }}>
                <Typography variant="h5" sx={{ fontWeight: "bold", mb: 1.5, textAlign: "center", fontSize: "1.09rem" }}>
                  What You’ll Get
                </Typography>
                <Box sx={{ display: "flex", flexDirection: "column", gap: 0.75 }}>
                  <Typography sx={{ fontSize: "0.9rem" }}>🎨 Beautiful charts in seconds</Typography>
                  <Typography sx={{ fontSize: "0.9rem" }}>⚡ Instant, actionable insights</Typography>
                  <Typography sx={{ fontSize: "0.9rem" }}>📤 No learning curve</Typography>
                  <Typography sx={{ fontSize: "0.9rem" }}>📈 Share compelling stories</Typography>
                </Box>
              </Paper>
            </Box>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default Home;
