import React from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import DatasetIcon from '@mui/icons-material/Dataset';
import BarChartIcon from '@mui/icons-material/BarChart';

export type PanelType = 'data' | 'charts';

interface LeftPanelNavProps {
  currentPanel: PanelType;
  onPanelChange: (panel: PanelType) => void;
}

const LeftPanelNav: React.FC<LeftPanelNavProps> = ({ currentPanel, onPanelChange }) => {
  return (
    <Box sx={{
      borderBottom: 1,
      borderColor: 'divider',
      backgroundColor: 'background.paper',
      boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
    }}>
      <Tabs
        value={currentPanel}
        onChange={(_, newValue) => onPanelChange(newValue)}
        aria-label="panel navigation"
        variant="fullWidth"
        sx={{
          '& .MuiTab-root': {
            minHeight: 80,
            fontSize: '0.875rem',
            fontWeight: 500,
          }
        }}
      >
        <Tab
          icon={<DatasetIcon sx={{ fontSize: 28 }} />}
          label="Bring Your Data"
          value="data"
          iconPosition="top"
        />
        <Tab
          icon={<BarChartIcon sx={{ fontSize: 28 }} />}
          label="View Your Charts"
          value="charts"
          iconPosition="top"
        />
      </Tabs>
    </Box>
  );
};

export default LeftPanelNav;