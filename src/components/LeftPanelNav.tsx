import React from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import DatasetIcon from '@mui/icons-material/Dataset';

export type PanelType = 'data';

interface LeftPanelNavProps {
  currentPanel: PanelType;
  onPanelChange: (panel: PanelType) => void;
}

const LeftPanelNav: React.FC<LeftPanelNavProps> = ({ currentPanel, onPanelChange }) => {
  return (
    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
      <Tabs
        value={currentPanel}
        onChange={(_, newValue) => onPanelChange(newValue)}
        aria-label="panel navigation"
        variant="fullWidth"
      >
        <Tab
          icon={<DatasetIcon />}
          label="Data"
          value="data"
          sx={{ minHeight: 72 }}
        />
      </Tabs>
    </Box>
  );
};

export default LeftPanelNav;