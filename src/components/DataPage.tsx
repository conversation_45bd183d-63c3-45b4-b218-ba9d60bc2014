import React from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import FileUpload from './FileUpload';

// Step-by-step loading states for a more engaging experience
const LOADING_STEPS = [
  'Reading Data',
  'Identifying Data Points',
  'Aggregating Data',
  'Identifying Suitable Charts',
  'Preparing Charts'
];

interface DataPageProps {
  onDataProcessed: (data: { charts: any[], available_columns: string[] }) => void;
  isLoading: boolean;
  loadingStep: number;
  showSteps: boolean;
}

const DataPage: React.FC<DataPageProps> = ({
  onDataProcessed,
  isLoading,
  loadingStep,
  showSteps
}) => {
  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      p: 4,
      minHeight: '600px',
    }}>
      {/* Page Title */}
      <Typography 
        variant="h4" 
        sx={{ 
          mb: 2,
          fontWeight: 'bold',
          textAlign: 'center',
          color: 'text.primary'
        }}
      >
        Bring Your Data
      </Typography>
      
      <Typography 
        variant="body1" 
        sx={{ 
          mb: 6,
          textAlign: 'center',
          color: 'text.secondary',
          maxWidth: '600px'
        }}
      >
        Upload your CSV or Excel files to generate beautiful, interactive charts automatically. 
        Our AI will analyze your data and create the most suitable visualizations.
      </Typography>

      {/* Loading Section */}
      {isLoading ? (
        <Box sx={{
          width: '100%',
          maxWidth: '500px',
          minHeight: '400px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'background.paper',
          borderRadius: '12px',
          p: 4,
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        }}>
          <CircularProgress size={60} sx={{ mb: 3 }} />
          <Typography variant="h6" sx={{ mb: 3, textAlign: 'center' }}>
            Processing your data...
          </Typography>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
            width: '100%',
          }}>
            {showSteps && LOADING_STEPS.map((step, idx) => (
              <Typography
                key={step}
                variant="body1"
                color={idx === loadingStep ? 'primary.main' : 'text.secondary'}
                sx={{
                  fontWeight: idx === loadingStep ? 600 : 400,
                  opacity: idx <= loadingStep ? 1 : 0.5,
                  transition: 'color 0.3s, opacity 0.3s',
                  textAlign: 'center',
                }}
              >
                {idx === loadingStep && '⏳ '}{step}
                {idx < loadingStep && ' ✓'}
              </Typography>
            ))}
          </Box>
        </Box>
      ) : (
        /* File Upload Section */
        <Box sx={{
          width: '100%',
          maxWidth: '500px',
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <FileUpload onDataProcessed={(data: any[]) => onDataProcessed({ charts: data, available_columns: [] })} />
        </Box>
      )}
    </Box>
  );
};

export default DataPage;
