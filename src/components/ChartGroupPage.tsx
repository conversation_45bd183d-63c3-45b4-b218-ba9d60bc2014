import React from 'react';
import { Box, IconButton, Paper } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Chart from './Chart';
import { ChartData } from './ChartGrid';

interface ChartGroupPageProps {
  groupName: string;
  charts: ChartData[];
  onBack: () => void;
  onChartUpdate: (index: number, updatedChart: ChartData) => void;
  gridBackgroundColor: string;
}

const ChartGroupPage: React.FC<ChartGroupPageProps> = ({
  charts,
  onBack,
  gridBackgroundColor
}) => {
  // Group charts into rows of 3
  const chartRows = [];
  for (let i = 0; i < charts.length; i += 3) {
    chartRows.push(charts.slice(i, i + 3));
  }

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      width: '100%',
      minHeight: '100vh',
    }}>
      {/* Header */}
      <Box sx={{
        width: '100%',
        backgroundColor: 'background.paper',
        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
        boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        p: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
      }}>
        <IconButton onClick={onBack} sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        
      
      </Box>

      {/* Body */}
      <Box sx={{
        width: '100%',
        flexGrow: 1,
        p: { xs: 2, md: 2 },
        backgroundColor: 'background.default',
        overflowY: 'auto',
      }}>
        <Box sx={{
          width: '100%',
          maxWidth: '1400px',
          margin: '0 auto',
          backgroundColor: gridBackgroundColor,
          borderRadius: '8px',
          p: 2,
        }}>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
          }}>
            {chartRows.map((rowCharts, rowIndex) => (
              <Box
                key={`row-${rowIndex}`}
                sx={{
                  display: 'flex',
                  width: '100%',
                  gap: 2,
                  justifyContent: 'flex-start',
                  flexWrap: 'wrap',
                }}
              >
                {rowCharts.map((chart, colIndex) => {
                  const chartIndex = rowIndex * 3 + colIndex;
                  
                  return chart && (
                    <Paper
                      key={`chart-${chartIndex}`}
                      elevation={3}
                      sx={{
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: 2,
                        overflow: 'hidden',
                        width: 'calc(33.33% - 12px)',
                        aspectRatio: '1/1',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                      }}
                    >
                      <Chart
                        data={chart}
                        position={colIndex === 0 ? "left" : colIndex === 2 ? "right" : "center"}
                        
                      />
                    </Paper>
                  );
                })}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ChartGroupPage;
