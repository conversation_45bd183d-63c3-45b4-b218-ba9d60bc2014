import React from 'react';
import { Box, Typography, IconButton, Paper } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Chart from './Chart';
import { ChartData } from './ChartGrid';

interface ChartGroupPageProps {
  groupName: string;
  charts: ChartData[];
  onBack: () => void;
  onChartUpdate: (index: number, updatedChart: ChartData) => void;
  gridBackgroundColor: string;
}

const ChartGroupPage: React.FC<ChartGroupPageProps> = ({
  groupName,
  charts,
  onBack,
  onChartUpdate,
  gridBackgroundColor
}) => {
  // Group charts into rows of 3
  const chartRows = [];
  for (let i = 0; i < charts.length; i += 3) {
    chartRows.push(charts.slice(i, i + 3));
  }

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      width: '100%',
      minHeight: '100vh',
    }}>
      {/* Header */}
      <Box sx={{
        width: '100%',
        backgroundColor: 'background.paper',
        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
        boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        p: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
      }}>
        <IconButton onClick={onBack} sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        
        <Box
          sx={{
            display: 'inline-block',
            background: 'linear-gradient(45deg, #FF4500, #FF8C00, #FFD700, #32CD32, #1E90FF, #8A2BE2, #FF1493)',
            backgroundSize: '400% 400%',
            padding: '8px 16px',
            borderRadius: '8px',
            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
            animation: 'gradientAnimation 10s ease infinite',
            '@keyframes gradientAnimation': {
              '0%': { backgroundPosition: '0% 50%' },
              '50%': { backgroundPosition: '100% 50%' },
              '100%': { backgroundPosition: '0% 50%' }
            }
          }}
        >
          <Typography
            variant="h1"
            sx={{
              fontSize: { xs: '1.2rem', md: '1.8rem' },
              fontWeight: 700,
              color: 'white',
              textAlign: 'left',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
              textTransform: 'capitalize',
            }}
          >
            {groupName} Charts ({charts.length})
          </Typography>
        </Box>
      </Box>

      {/* Body */}
      <Box sx={{
        width: '100%',
        flexGrow: 1,
        p: { xs: 2, md: 2 },
        backgroundColor: 'background.default',
        overflowY: 'auto',
      }}>
        <Box sx={{
          width: '100%',
          maxWidth: '1400px',
          margin: '0 auto',
          backgroundColor: gridBackgroundColor,
          borderRadius: '8px',
          p: 2,
        }}>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
          }}>
            {chartRows.map((rowCharts, rowIndex) => (
              <Box
                key={`row-${rowIndex}`}
                sx={{
                  display: 'flex',
                  width: '100%',
                  gap: 2,
                  justifyContent: 'flex-start',
                  flexWrap: 'wrap',
                }}
              >
                {rowCharts.map((chart, colIndex) => {
                  const chartIndex = rowIndex * 3 + colIndex;
                  
                  return chart && (
                    <Paper
                      key={`chart-${chartIndex}`}
                      elevation={3}
                      sx={{
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: 2,
                        overflow: 'hidden',
                        width: 'calc(33.33% - 12px)',
                        aspectRatio: '1/1',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                      }}
                    >
                      <Chart
                        data={chart}
                        position={colIndex === 0 ? "left" : colIndex === 2 ? "right" : "center"}
                        onChartUpdate={(updatedChart) => onChartUpdate(chartIndex, updatedChart)}
                      />
                    </Paper>
                  );
                })}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ChartGroupPage;
