import React from "react";
import { Box, Typography, Paper, useTheme } from "@mui/material";
import { GoogleOAuthProvider, GoogleLogin } from "@react-oauth/google";
import config from "../config";
import { googleLogin,initializeSession  } from "../services/apiService";

interface SignInProps {
  onSignInSuccess: () => void;
}

const SignIn: React.FC<SignInProps> = ({ onSignInSuccess }) => {
  const theme = useTheme();

  const handleGoogleSuccess = async (credentialResponse: any) => {
    console.log("Google login success:", credentialResponse);

    initializeSession();

    const token = credentialResponse.credential;
    if (!token) {
      console.error("No token received from Google");
      return;
    }

    try {
      const response = await googleLogin(token);
      console.log("Backend auth success:", response);

      // Example: save tokens / user details
      localStorage.setItem("access_token", response.access_token);
      localStorage.setItem("refresh_token", response.refresh_token);

      onSignInSuccess();
    } catch (error) {
      console.error("Backend auth failed:", error);
    }
  };

  const handleGoogleFailure = () => {
    console.error("Google login failed");
  };

  return (
    <Box
      sx={{
        minHeight: "80vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: theme.palette.background.default,
        p: 2,
      }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 5,
          borderRadius: 4,
          maxWidth: 400,
          width: "100%",
          textAlign: "center",
          backgroundColor: "#f8f9fa",
        }}
      >
        <Typography variant="h3" gutterBottom>
          Become a Data Storyteller
        </Typography>

        <GoogleOAuthProvider clientId={config.googleClientId}>
          <GoogleLogin
            onSuccess={handleGoogleSuccess}
            onError={handleGoogleFailure}
          />
        </GoogleOAuthProvider>
      </Paper>
    </Box>
  );
};

export default SignIn;
