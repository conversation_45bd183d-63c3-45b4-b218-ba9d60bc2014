import React from "react";
import { useDropzone } from "react-dropzone";
import { Box, Paper, Typography } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { uploadCSV } from '../services/apiService';

interface FileUploadProps {
  onDataProcessed: (data: any[]) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onDataProcessed }) => {

  const [file, setFile] = React.useState<File | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'text/csv': ['.csv']
    },
    multiple: false,
    onDrop: async (acceptedFiles) => {
      const selectedFile = acceptedFiles[0];
      setFile(selectedFile);
      if (selectedFile) {
        setIsLoading(true);
        await handleUpload(selectedFile);
        setIsLoading(false);
      }
    }
  });

  const handleUpload = async (uploadFile: File | null = null) => {
    const fileToUpload = uploadFile || file;
    if (!fileToUpload) return;
    const formData = new FormData();
    formData.append('file', fileToUpload);
    try {
      const data = await uploadCSV(formData);
      if (data && Array.isArray(data.charts)) {
        const processedCharts = data.charts.map((chart: any) => {
          return {
            chart_type: chart.chart_type,
            chart_group: chart.chart_group, // Include chart_group field
            library: chart.library,
            data: chart.data,
            layout: chart.layout
          };
        });
        onDataProcessed(processedCharts);
        setFile(null);
      }
    } catch (error) {
      console.error('Error uploading CSV:', error);
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper
        {...getRootProps()}
        sx={{
          p: 1.5,
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.500',
          borderRadius: 2,
          backgroundColor: 'background.default',
          cursor: isLoading ? 'default' : 'pointer',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            borderColor: isLoading ? 'grey.500' : 'primary.main',
            backgroundColor: 'background.default',
          },
          opacity: isLoading ? 0.7 : 1,
          pointerEvents: isLoading ? 'none' : 'auto',
          position: 'relative'
        }}
      >
        <input {...getInputProps()} disabled={isLoading} />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1.5
          }}
        >
          {isLoading ? (
            <CloudUploadIcon
              sx={{
                fontSize: 28,
                color: isDragActive ? 'primary.main' : 'grey.500'
              }}
            />
          ) : (
            <CloudUploadIcon
              sx={{
                fontSize: 28,
                color: isDragActive ? 'primary.main' : 'grey.500'
              }}
            />
          )}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Typography variant="body2" align="left">
              {isLoading
                ? "Processing file..."
                : isDragActive
                  ? "Drop CSV file here"
                  : "Drag & drop CSV file, or click to select"}
            </Typography>
            {file && !isLoading && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                Selected: {file.name}
              </Typography>
            )}
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default FileUpload;
