import React from "react";
import { useDropzone } from "react-dropzone";
import { Box, Paper, Typography } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { uploadCSV } from '../services/apiService';

interface FileUploadProps {
  onDataProcessed: (data: any[]) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onDataProcessed }) => {

  const [file, setFile] = React.useState<File | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    multiple: false,
    onDrop: async (acceptedFiles) => {
      const selectedFile = acceptedFiles[0];
      setFile(selectedFile);
      if (selectedFile) {
        setIsLoading(true);
        await handleUpload(selectedFile);
        setIsLoading(false);
      }
    }
  });

  const handleUpload = async (uploadFile: File | null = null) => {
    const fileToUpload = uploadFile || file;
    if (!fileToUpload) return;
    const formData = new FormData();
    formData.append('file', fileToUpload);
    try {
      const data = await uploadCSV(formData);
      if (data && Array.isArray(data.charts)) {
        const processedCharts = data.charts.map((chart: any) => {
          return {
            chart_type: chart.chart_type,
            chart_group: chart.chart_group, // Include chart_group field
            library: chart.library,
            data: chart.data,
            layout: chart.layout
          };
        });
        onDataProcessed(processedCharts);
        setFile(null);
      }
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      <Paper
        {...getRootProps()}
        sx={{
          width: '100%',
          height: '100%',
          minHeight: '300px',
          p: 4,
          border: '3px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.400',
          borderRadius: 3,
          backgroundColor: isDragActive ? 'primary.50' : 'background.default',
          cursor: isLoading ? 'default' : 'pointer',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            borderColor: isLoading ? 'grey.400' : 'primary.main',
            backgroundColor: isLoading ? 'background.default' : 'primary.50',
            transform: isLoading ? 'none' : 'scale(1.02)',
          },
          opacity: isLoading ? 0.7 : 1,
          pointerEvents: isLoading ? 'none' : 'auto',
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <input {...getInputProps()} disabled={isLoading} />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 3,
            textAlign: 'center',
          }}
        >
          <CloudUploadIcon
            sx={{
              fontSize: 80,
              color: isDragActive ? 'primary.main' : 'grey.500',
              transition: 'all 0.3s ease-in-out',
            }}
          />
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography variant="h6" align="center" sx={{ fontWeight: 600 }}>
              {isLoading
                ? "Processing file..."
                : isDragActive
                  ? "Drop your file here"
                  : "Upload your data file"}
            </Typography>
            <Typography variant="body2" color="text.secondary" align="center">
              {isLoading
                ? "Please wait while we process your data..."
                : "Drag & drop CSV or Excel files, or click to select"}
            </Typography>
            <Typography variant="caption" color="text.secondary" align="center">
              Supported formats: .csv, .xlsx, .xls
            </Typography>
            {file && !isLoading && (
              <Typography variant="body2" color="primary.main" sx={{ mt: 1, fontWeight: 500 }}>
                Selected: {file.name}
              </Typography>
            )}
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default FileUpload;
