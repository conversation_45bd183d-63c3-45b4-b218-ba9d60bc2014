import React, { useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Switch,
  FormControlLabel,
  Divider,
  useTheme,
  Button,
} from "@mui/material";

const Pricing: React.FC = () => {
  const [yearly, setYearly] = useState(true);
  const theme = useTheme();

  const handleToggle = () => {
    setYearly(!yearly);
  };

  const plans = [
    {
      name: "Free",
      price: "0$",
      yearlyPrice: "0$",
      credits: "10 AI credits / month",
    },
    {
      name: "Starter",
      price: "2$",
      yearlyPrice: "1.5$",
      credits: "100 AI credits / month",
    },
    {
      name: "Pro",
      price: "8$",
      yearlyPrice: "6$",
      credits: "1000 AI credits / month",
    },
  ];

  return (
    <Box>
      {/* Pricing Header */}
      <Box sx={{ textAlign: "center", mt: 6, mb: 3 }}>
        <Typography variant="h3" fontWeight="bold">
          Pricing Plans
        </Typography>

        <FormControlLabel
          control={<Switch checked={yearly} onChange={handleToggle} color="primary" />}
          label={
            <Typography sx={{ ml: 1 }}>
              {yearly ? "Yearly (Save 25%)" : "Monthly"}
            </Typography>
          }
          sx={{ mt: 2 }}
        />
      </Box>

      {/* Pricing Cards Section */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: 4, px: { xs: 2, md: 10 } }}>
        {plans.map((plan) => (
          <Box key={plan.name} sx={{ flex: '1 1 300px', minWidth: 250, maxWidth: 400, mb: 2 }}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                borderRadius: 3,
                backgroundColor: theme.palette.grey[50],
                textAlign: "center",
                display: "flex",
                flexDirection: "column",
                gap: 2,
              }}
            >
              <Typography variant="h5" fontWeight="bold">
                {plan.name}
              </Typography>

              <Typography
                variant="h3"
                sx={{ color: theme.palette.primary.main, fontWeight: "bold" }}
              >
                {yearly ? plan.yearlyPrice : plan.price}
              </Typography>

              <Divider sx={{ my: 1 }} />

              <Typography variant="subtitle1">{plan.credits}</Typography>
              <Typography variant="subtitle1">
                Unlimited visuals editing
              </Typography>
              <Typography variant="subtitle1">
                Unlimited file import (Excel, CSV)
              </Typography>
              <Typography variant="subtitle1">Unlimited PNG export</Typography>
              <Typography variant="subtitle1">
                Built-in styles and fonts
              </Typography>

              <Button
                variant="contained"
                color="primary"
                sx={{ mt: 2, textTransform: "none" }}
              >
                {plan.name === "Free" ? "Get Started" : "Subscribe"}
              </Button>
            </Paper>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default Pricing;
