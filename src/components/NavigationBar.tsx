import React from "react";
import {
  AppBar,
  Box,
  Toolbar,
  Typography,
  Link as MuiLink,
} from "@mui/material";

interface NavigationBarProps {
  onNavigate: (page: string) => void;
  isSignedIn: boolean;
  currentPage: string;
}

const NavigationBar: React.FC<NavigationBarProps> = ({ onNavigate, isSignedIn, currentPage }) => {
 
  return (
    <AppBar position="static" color="transparent" elevation={0}>
      <Toolbar sx={{ justifyContent: "space-between", mt: 1.5 }}>
        <Box
          sx={{
            display: "inline-block",
            background:
              "linear-gradient(45deg, #FF4500, #FF8C00, #FFD700, #32CD32, #1E90FF, #8A2BE2, #FF1493)",
            backgroundSize: "400% 400%",
            padding: "8px 16px",
            borderRadius: "8px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
            animation: "gradientAnimation 10s ease infinite",
            "@keyframes gradientAnimation": {
              "0%": { backgroundPosition: "0% 50%" },
              "50%": { backgroundPosition: "100% 50%" },
              "100%": { backgroundPosition: "0% 50%" },
            },
          }}
        >
          <Typography
            variant="h1"
            sx={{
              fontSize: { xs: "1.25rem", md: "1.5rem" },
              fontWeight: 700,
              color: "white",
              textAlign: "left",
              textShadow: "1px 1px 2px rgba(0,0,0,0.3)",
            }}
          >
            DCharti5
          </Typography>
        </Box>

        <Box sx={{ display: "flex", gap: 3 }}>
            <MuiLink
                href="#"
                underline="none"
                color="text.primary"
                variant="button"
                onClick={() => onNavigate("home")}
                sx={{ fontWeight: currentPage === "home" ? "bold" : "normal" }}
            >
                Home
            </MuiLink>
            {/* <MuiLink
                href="#"
                underline="none"
                color="text.primary"
                variant="button"
                onClick={() => onNavigate("pricing")}
                sx={{ fontWeight: currentPage === "pricing" ? "bold" : "normal" }}
            >
                Pricing
            </MuiLink> */}
            {!isSignedIn && (
                <MuiLink
                href="#"
                underline="none"
                color="text.primary"
                variant="button"
                onClick={() => onNavigate("signin")}
                sx={{ fontWeight: currentPage === "signin" ? "bold" : "normal" }}
                >
                Sign In
            </MuiLink>
            )}
        </Box>

      </Toolbar>
    </AppBar>
  );
};

export default NavigationBar;
