import React, { useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import ChartGrid from './ChartGrid';

// Sample data with multiple chart groups
const sampleGroupedCharts = [
  {
    id: 'chart-1',
    chart_type: 'pie',
    chart_group: 'basic',
    library: 'plotly',
    data: {
      values: [30, 25, 20, 15, 10],
      labels: ['Category A', 'Category B', 'Category C', 'Category D', 'Category E'],
      type: 'pie'
    },
    layout: {
      title: 'Pie Chart - Basic Group',
      showlegend: true
    }
  },
  {
    id: 'chart-2',
    chart_type: 'bar',
    chart_group: 'basic',
    library: 'plotly',
    data: {
      x: ['A', 'B', 'C', 'D'],
      y: [20, 14, 23, 25],
      type: 'bar'
    },
    layout: {
      title: 'Bar Chart - Basic Group',
      xaxis: { title: 'Categories' },
      yaxis: { title: 'Values' }
    }
  },
  {
    id: 'chart-3',
    chart_type: 'doughnut',
    chart_group: 'basic',
    library: 'plotly',
    data: {
      values: [237, 246, 259, 258],
      labels: ['East', 'North', 'South', 'West'],
      type: 'pie',
      hole: 0.4
    },
    layout: {
      title: 'Doughnut Chart - Basic Group',
      showlegend: true
    }
  },
  {
    id: 'chart-4',
    chart_type: 'histogram',
    chart_group: 'numerical',
    library: 'plotly',
    data: {
      x: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      type: 'histogram'
    },
    layout: {
      title: 'Histogram - Numerical Group',
      xaxis: { title: 'Value' },
      yaxis: { title: 'Frequency' }
    }
  },
  {
    id: 'chart-5',
    chart_type: 'scatter',
    chart_group: 'numerical',
    library: 'plotly',
    data: {
      x: [1, 2, 3, 4, 5],
      y: [2, 4, 3, 5, 6],
      type: 'scatter',
      mode: 'markers'
    },
    layout: {
      title: 'Scatter Plot - Numerical Group',
      xaxis: { title: 'X Values' },
      yaxis: { title: 'Y Values' }
    }
  },
  {
    id: 'chart-6',
    chart_type: 'line',
    chart_group: 'time_series',
    library: 'plotly',
    data: {
      x: ['Jan 2024', 'Feb 2024', 'Mar 2024'],
      y: [100, 120, 110],
      type: 'scatter',
      mode: 'lines'
    },
    layout: {
      title: 'Line Chart - Time Series Group',
      xaxis: { title: 'Date' },
      yaxis: { title: 'Value' }
    }
  }
];

const TestChartGrouping: React.FC = () => {
  const [charts, setCharts] = useState<any[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<{ name: string; charts: any[] } | null>(null);

  const loadTestData = () => {
    setCharts(sampleGroupedCharts);
  };

  const clearData = () => {
    setCharts([]);
    setSelectedGroup(null);
  };

  const handleGroupClick = (groupName: string, groupCharts: any[]) => {
    setSelectedGroup({ name: groupName, charts: groupCharts });
  };

  const handleChartUpdate = (index: number, updatedChart: any) => {
    console.log('Chart updated:', index, updatedChart);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Chart Grouping Test
      </Typography>
      
      <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
        <Button variant="contained" onClick={loadTestData}>
          Load Test Data (6 charts, 3 groups)
        </Button>
        <Button variant="outlined" onClick={clearData}>
          Clear Data
        </Button>
      </Box>

      {selectedGroup && (
        <Box sx={{ mb: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
          <Typography variant="h6">
            Selected Group: {selectedGroup.name} ({selectedGroup.charts.length} charts)
          </Typography>
          <Button 
            variant="text" 
            onClick={() => setSelectedGroup(null)}
            sx={{ mt: 1 }}
          >
            Back to Dashboard View
          </Button>
        </Box>
      )}

      {charts.length > 0 ? (
        <Box sx={{ bgcolor: '#f5f5f5', borderRadius: 2, p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Expected: 3 chart groups (basic, numerical, time_series)
          </Typography>
          <Typography variant="body2" gutterBottom sx={{ mb: 2 }}>
            • Basic group: 3 charts (pie, bar, doughnut) - should show "+2" button
            • Numerical group: 2 charts (histogram, scatter) - should show "+1" button  
            • Time series group: 1 chart (line) - no button
          </Typography>
          
          <ChartGrid
            charts={charts}
            availableColumns={[]}
            onChartUpdate={handleChartUpdate}
            onGroupClick={handleGroupClick}
            gridBackgroundColor="#f5f5f5"
          />
        </Box>
      ) : (
        <Typography variant="body1" color="text.secondary">
          Click "Load Test Data" to see the chart grouping in action.
        </Typography>
      )}
    </Box>
  );
};

export default TestChartGrouping;
