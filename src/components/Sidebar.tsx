import React from "react";
import { Box, Drawer, List, ListItem, ListItemText, Divider, IconButton } from "@mui/material";
import { Menu as MenuIcon } from '@mui/icons-material';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectChart: (chartIndex: number) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, onSelectChart }) => {
  const chartTitles = ["Chart 1", "Chart 2", "Chart 3"]; // Placeholder chart titles
  
  return (
    <Drawer
      anchor="left"
      open={isOpen}
      onClose={onClose}
      sx={{
        width: 240,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 240,
          boxSizing: 'border-box',
        },
      }}
    >
      <Box sx={{ width: 250 }}>
        <IconButton
          sx={{ margin: 2 }}
          color="primary"
          onClick={onClose}
        >
          <MenuIcon />
        </IconButton>
        
        <List>
          {chartTitles.map((title, index) => (
            <ListItem key={index} onClick={() => onSelectChart(index)} sx={{ cursor: 'pointer' }}>
              <ListItemText primary={title} />
            </ListItem>
          ))}
        </List>
        
        <Divider />
        
        {/* Add any other menu items or features here */}
      </Box>
    </Drawer>
  );
};

export default Sidebar;
