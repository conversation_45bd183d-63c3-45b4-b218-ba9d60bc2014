import React, { useState } from 'react';
import { Box, Typography, CircularProgress, Snackbar, Alert } from '@mui/material';
import FileUpload from './FileUpload';
import ChartGrid from './ChartGrid';
import ChartGroupPage from './ChartGroupPage';
import useChartData from '../hooks/useChartData';

// Step-by-step loading states for a more engaging experience
const LOADING_STEPS = [
  'Reading Data',
  'Identifying Data Points',
  'Aggregating Data',
  'Identifying Suitable Charts',
  'Preparing Charts'
];

const Dashboard: React.FC<{ onNavigate: (page: string) => void }> = ({ }) => {
  const {
    charts,
    addChartData,
    updateChartData,
    resetChartData,
  } = useChartData();

  const [bgColor] = useState('#f5f5f5');
  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [isClearing, setIsClearing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingStep, setLoadingStep] = useState(0);
  const [showSteps, setShowSteps] = useState(false);
  const [currentView, setCurrentView] = useState<'dashboard' | 'group'>('dashboard');
  const [selectedGroup, setSelectedGroup] = useState<{ name: string; charts: any[] } | null>(null);

  const clearError = () => setError(null);

  // Helper to animate loading steps
  const runLoadingSteps = async () => {
    setShowSteps(true);
    for (let i = 0; i < LOADING_STEPS.length; i++) {
      setLoadingStep(i);
      await new Promise(resolve => setTimeout(resolve, 600));
    }
  };

  const handleDataProcessed = async (responseData: { charts: any[], available_columns: string[] }) => {
    setIsClearing(true);
    setLoadingStep(0);
    await runLoadingSteps();
    resetChartData();
    await new Promise(resolve => setTimeout(resolve, 300));

    try {
      if (responseData && Array.isArray(responseData.charts) && responseData.charts.length > 0) {
        const chartsWithIds = responseData.charts.map((chart, index) => ({
          ...chart,
          id: chart.id || `chart-${Date.now()}-${index}`,
        }));
        addChartData(chartsWithIds);
        setAvailableColumns(responseData.available_columns || []);
      } else {
        throw new Error("Invalid or empty chart data received");
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while processing data.');
    }

    setIsClearing(false);
    setShowSteps(false); // Hide steps after processing
  };

  const handleChartUpdate = (id: string | number, updatedChartData: any) => {
    updateChartData(Number(id), updatedChartData);
  };

  const handleGroupClick = (groupName: string, groupCharts: any[]) => {
    setSelectedGroup({ name: groupName, charts: groupCharts });
    setCurrentView('group');
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setSelectedGroup(null);
  };

  const handleGroupChartUpdate = (index: number, updatedChart: any) => {
    if (selectedGroup) {
      const updatedCharts = [...selectedGroup.charts];
      updatedCharts[index] = updatedChart;
      setSelectedGroup({ ...selectedGroup, charts: updatedCharts });

      // Also update the main charts array
      const originalIndex = charts.findIndex(chart => chart.id === updatedChart.id);
      if (originalIndex !== -1) {
        updateChartData(originalIndex, updatedChart);
      }
    }
  };

  // Render group page if a group is selected
  if (currentView === 'group' && selectedGroup) {
    return (
      <ChartGroupPage
        groupName={selectedGroup.name}
        charts={selectedGroup.charts}
        onBack={handleBackToDashboard}
        onChartUpdate={handleGroupChartUpdate}
        gridBackgroundColor={bgColor}
      />
    );
  }

  // Render main dashboard
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', minHeight: '100vh' }}>
      {/* Header Row */}
      <Box sx={{
        width: '100%',
        backgroundColor: 'background.paper',
        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
        boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        p: 2,
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        alignItems: { xs: 'flex-start', md: 'center' },
        gap: { xs: 2, md: 3 },
      }}>
        

        {/* File Upload Box */}
        <Box sx={{
          flexGrow: 1,
          maxWidth: { xs: '100%', md: '500px' },
        }}>
          <FileUpload onDataProcessed={(data: any[]) => handleDataProcessed({ charts: data, available_columns: [] })} />
        </Box>
      </Box>

      {/* Body Row */}
      <Box sx={{
        width: '100%',
        flexGrow: 1,
        p: { xs: 2, md: 2 },
        backgroundColor: 'background.default',
        overflowY: 'auto',
      }}>
        <Box sx={{
          width: '100%',
          maxWidth: '1400px',
          margin: '0 auto',
          display: 'flex',
          flexDirection: 'column',
          gap: 2, // Reduced gap to show more content
        }}>

          {/* Charts Section */}
          {isClearing ? (
            <Box sx={{
              width: '100%',
              minHeight: '220px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: bgColor,
              borderRadius: '8px',
              p: 3,
            }}>
              <CircularProgress size={40} sx={{ mb: 2 }} />
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 1,
              }}>
                {showSteps && LOADING_STEPS.map((step, idx) => (
                  <Typography
                    key={step}
                    variant="h6"
                    color={idx === loadingStep ? 'primary.main' : 'text.secondary'}
                    sx={{
                      fontWeight: idx === loadingStep ? 700 : 400,
                      opacity: idx <= loadingStep ? 1 : 0.5,
                      transition: 'color 0.3s, opacity 0.3s',
                    }}
                  >
                    {step}
                  </Typography>
                ))}
              </Box>
            </Box>
          ) : charts.length > 0 ? (
            <Box sx={{
              width: '100%',
              backgroundColor: bgColor,
              borderRadius: '8px',
              p: 2,
            }}>
              <ChartGrid
                charts={charts}
                availableColumns={availableColumns}
                onChartUpdate={handleChartUpdate}
                onGroupClick={handleGroupClick}
                gridBackgroundColor={bgColor}
              />
            </Box>
          ) : (
            <Box sx={{
              minHeight: '120px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{
                  textAlign: 'center',
                  maxWidth: '400px',
                }}
              >
                Upload a CSV file to generate charts.
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      {/* Snackbar for errors */}
      <Snackbar
        open={!!error}
        autoHideDuration={4000}
        onClose={clearError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="error" onClose={clearError} variant="filled">
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Dashboard;
