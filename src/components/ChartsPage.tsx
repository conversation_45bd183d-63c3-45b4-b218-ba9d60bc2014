import React, { useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import ChartGrid from './ChartGrid';
import ChartGroupPage from './ChartGroupPage';
import DatasetIcon from '@mui/icons-material/Dataset';

interface ChartsPageProps {
  charts: any[];
  availableColumns: string[];
  onChartUpdate: (id: string | number, updatedChartData: any) => void;
  gridBackgroundColor: string;
  onSwitchToDataPage: () => void;
}

const ChartsPage: React.FC<ChartsPageProps> = ({
  charts,
  availableColumns,
  onChartUpdate,
  gridBackgroundColor,
  onSwitchToDataPage
}) => {
  const [currentView, setCurrentView] = useState<'dashboard' | 'group'>('dashboard');
  const [selectedGroup, setSelectedGroup] = useState<{ name: string; charts: any[] } | null>(null);

  const handleGroupClick = (groupName: string, groupCharts: any[]) => {
    setSelectedGroup({ name: groupName, charts: groupCharts });
    setCurrentView('group');
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setSelectedGroup(null);
  };

  const handleGroupChartUpdate = (index: number, updatedChart: any) => {
    if (selectedGroup) {
      const updatedCharts = [...selectedGroup.charts];
      updatedCharts[index] = updatedChart;
      setSelectedGroup({ ...selectedGroup, charts: updatedCharts });

      // Also update the main charts array
      const originalIndex = charts.findIndex(chart => chart.id === updatedChart.id);
      if (originalIndex !== -1) {
        onChartUpdate(originalIndex, updatedChart);
      }
    }
  };

  // Render group page if a group is selected
  if (currentView === 'group' && selectedGroup) {
    return (
      <ChartGroupPage
        groupName={selectedGroup.name}
        charts={selectedGroup.charts}
        onBack={handleBackToDashboard}
        onChartUpdate={handleGroupChartUpdate}
        gridBackgroundColor={gridBackgroundColor}
      />
    );
  }

  // Render main charts page
  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      p: 3,
    }}>
      {/* Page Header */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
      }}>
        <Box>
          <Typography 
            variant="h4" 
            sx={{ 
              fontWeight: 'bold',
              color: 'text.primary',
              mb: 1,
            }}
          >
            Your Charts
          </Typography>
          <Typography 
            variant="body1" 
            sx={{ 
              color: 'text.secondary',
            }}
          >
            {charts.length} chart{charts.length !== 1 ? 's' : ''} generated from your data
          </Typography>
        </Box>
        
        <Button
          variant="outlined"
          startIcon={<DatasetIcon />}
          onClick={onSwitchToDataPage}
          sx={{
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 500,
          }}
        >
          Upload New Data
        </Button>
      </Box>

      {/* Charts Section */}
      {charts.length > 0 ? (
        <Box sx={{
          width: '100%',
          backgroundColor: gridBackgroundColor,
          borderRadius: '12px',
          p: 3,
          flexGrow: 1,
          overflow: 'auto',
        }}>
          <ChartGrid
            charts={charts}
            availableColumns={availableColumns}
            onChartUpdate={onChartUpdate}
            onGroupClick={handleGroupClick}
            gridBackgroundColor={gridBackgroundColor}
          />
        </Box>
      ) : (
        <Box sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'background.paper',
          borderRadius: '12px',
          p: 4,
          border: '2px dashed',
          borderColor: 'grey.300',
        }}>
          <DatasetIcon sx={{ fontSize: 80, color: 'grey.400', mb: 2 }} />
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{
              textAlign: 'center',
              mb: 2,
            }}
          >
            No charts available
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              textAlign: 'center',
              mb: 3,
              maxWidth: '400px',
            }}
          >
            Upload your data files to generate beautiful charts and visualizations automatically.
          </Typography>
          <Button
            variant="contained"
            startIcon={<DatasetIcon />}
            onClick={onSwitchToDataPage}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
            }}
          >
            Bring Your Data
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default ChartsPage;
