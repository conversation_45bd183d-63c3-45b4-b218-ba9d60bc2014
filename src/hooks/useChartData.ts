// src/hooks/useChartData.ts
import { useState, useCallback } from 'react';
import type { ChartData } from '../components/ChartGrid';

const useChartData = () => {
  const [charts, setCharts] = useState<ChartData[]>([]);

  // Add new chart data to the state
  const addChartData = useCallback((newChartData: ChartData[]) => {
    setCharts(prevCharts => [...prevCharts, ...newChartData]);
  }, []);

  // Reset all chart data
  const resetChartData = useCallback(() => {
    setCharts([]);
  }, []);

  // Update specific chart data (if needed)
  const updateChartData = useCallback((index: number, updatedChartData: ChartData) => {
    setCharts(prevCharts => {
      const updatedCharts = [...prevCharts];
      updatedCharts[index] = updatedChartData;
      return updatedCharts;
    });
  }, []);

  return {
    charts,
    addChartData,
    resetChartData,
    updateChartData,
  };
};

export default useChartData;
