import React, { useState } from "react";
import { Theme<PERSON>rovider, CssBaseline, Box } from "@mui/material";
import Dashboard from "./components/Dashboard";
import Home from "./components/Home";
import SignIn from "./components/SignIn";
import Pricing from "./components/Pricing";
import NavigationBar from "./components/NavigationBar";
import { theme } from "./theme";
import { tryRefreshToken } from "./services/apiService";

// Helper to check if user is signed in and token is valid/refreshable
function isUserSignedIn() {
  const accessToken = localStorage.getItem("access_token");
  if (!accessToken) return false;
  // Optionally, check expiry here if you store expiry in localStorage
  return true;
}

const App: React.FC = () => {
  const [currentPage, setCurrentPage] = useState("home");
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  React.useEffect(() => {
    // On mount, check if access_token exists and is valid, else try refresh
    const checkAuth = async () => {
      const accessToken = localStorage.getItem("access_token");
      if (accessToken) {
        // Optionally, check expiry here if you store expiry in localStorage
        setCurrentPage("dashboard");
        setIsCheckingAuth(false);
        return;
      }
      // Try refresh if refresh_token exists
      const refreshToken = localStorage.getItem("refresh_token");
      if (refreshToken) {
        const refreshed = await tryRefreshToken();
        if (refreshed) {
          setCurrentPage("dashboard");
        } else {
          setCurrentPage("signin");
        }
      } else {
        setCurrentPage("home");
      }
      setIsCheckingAuth(false);
    };
    checkAuth();
  }, []);

  const handleStart = async () => {
    // If already signed in, go to dashboard
    if (isUserSignedIn()) {
      setCurrentPage("dashboard");
      return;
    }
    // Try refresh if refresh_token exists
    const refreshToken = localStorage.getItem("refresh_token");
    if (refreshToken) {
      const refreshed = await tryRefreshToken();
      if (refreshed) {
        setCurrentPage("dashboard");
        return;
      }
    }
    setCurrentPage("signin");
  };

  const handleSignInSuccess = () => {
    setCurrentPage("dashboard");
  };

  const renderPage = () => {
    if (isCheckingAuth) return null; // Or a loader
    switch (currentPage) {
      case "dashboard":
        return <Dashboard onNavigate={setCurrentPage} />;
      case "pricing":
        return <Pricing />;
      case "signin":
        return <SignIn onSignInSuccess={handleSignInSuccess} />;
      default:
        return <Home onStart={handleStart} />;
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          minHeight: "100vh",
          background: `linear-gradient(135deg, ${theme.palette.background.default} 0%, #ffffff 100%)`,
        }}
      >
        <NavigationBar
          onNavigate={setCurrentPage}
          isSignedIn={isUserSignedIn()}
          currentPage={currentPage}
        />
        {renderPage()}
      </Box>
    </ThemeProvider>
  );
};

export default App;
